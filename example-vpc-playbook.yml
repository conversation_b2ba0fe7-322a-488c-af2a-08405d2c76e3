---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        example-vpc-playbook.yml                                          #
# Version:                                                                        #
#               2024-10-05 Simplified version without dependencies                #
# Create Date:  2024-10-05                                                        #
# Description:                                                                    #
#               Simple example playbook for aws_tfvpc role                        #
#               Uses only VPC role variables without external dependencies        #
#                                                                                 #
# Usage:                                                                          #
#   ansible-playbook example-vpc-playbook.yml                                     #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Deploy AWS VPC Infrastructure using Terraform
  hosts: localhost
  gather_facts: true
  connection: local

  vars:
    # Simple VPC Configuration - all variables in one place
    aws_tfvpc_config:
      # AWS Settings
      account: "************"  # Your AWS account ID
      region: "us-east-1"      # AWS region
      aws_profile: "default"   # AWS CLI profile

      # Basic Settings
      environment: "dev"
      vpc_init: true

      # Network Configuration
      vpc_cidr_block: "10.0.0.0/16"
      public_subnet_cidr: "********/24"
      availability_zone: "us-east-1a"  # Leave empty for auto-selection

      # VPC Settings
      enable_dns_support: true
      enable_dns_hostnames: true
      map_public_ip_on_launch: true

      # Additional Tags
      additional_tags:
        Project: "VPC-Demo"
        Owner: "DevOps-Team"
        Environment: "dev"

      # Terraform Execution Control
      terraform_init: true
      terraform_plan: true
      terraform_apply: true
      terraform_destroy: false # Set to true to destroy infrastructure

      # Cleanup
      cleanup_temp_files: true

  tasks:
    # ------------------------------------------------------------------------------- #
    - name: Deploy VPC infrastructure using aws_tfvpc role
      ansible.builtin.include_role:
        name: aws_tfvpc

    # ------------------------------------------------------------------------------- #
    - name: Display VPC deployment results
      ansible.builtin.debug:
        msg: |
          VPC Deployment Results:
          ======================
          VPC ID: {{ aws_tfvpc_outputs.vpc_id.value | default('N/A') }}
          VPC CIDR: {{ aws_tfvpc_outputs.vpc_cidr_block.value | default('N/A') }}
          Public Subnet ID: {{ aws_tfvpc_outputs.public_subnet_id.value | default('N/A') }}
          Public Subnet CIDR: {{ aws_tfvpc_outputs.public_subnet_cidr_block.value | default('N/A') }}
          Internet Gateway ID: {{ aws_tfvpc_outputs.internet_gateway_id.value | default('N/A') }}
          Route Table ID: {{ aws_tfvpc_outputs.public_route_table_id.value | default('N/A') }}
          Availability Zone: {{ aws_tfvpc_outputs.availability_zone.value | default('N/A') }}
      when: aws_tfvpc_outputs is defined

    # ------------------------------------------------------------------------------- #
    - name: Save VPC outputs to file
      ansible.builtin.copy:
        content: |
          # VPC Deployment Outputs
          # Generated: {{ ansible_date_time.iso8601 }}

          VPC_ID="{{ aws_tfvpc_outputs.vpc_id.value | default('') }}"
          VPC_CIDR="{{ aws_tfvpc_outputs.vpc_cidr_block.value | default('') }}"
          PUBLIC_SUBNET_ID="{{ aws_tfvpc_outputs.public_subnet_id.value | default('') }}"
          PUBLIC_SUBNET_CIDR="{{ aws_tfvpc_outputs.public_subnet_cidr_block.value | default('') }}"
          INTERNET_GATEWAY_ID="{{ aws_tfvpc_outputs.internet_gateway_id.value | default('') }}"
          PUBLIC_ROUTE_TABLE_ID="{{ aws_tfvpc_outputs.public_route_table_id.value | default('') }}"
          AVAILABILITY_ZONE="{{ aws_tfvpc_outputs.availability_zone.value | default('') }}"
        dest: "./vpc-outputs.env"
        mode: '0644'
      when: aws_tfvpc_outputs is defined
