#############################################################################################
#                                                                                           #
# Title:        variables.tf                                                                #
# Version:                                                                                  #
#               2024-10-05 Initial creation for VPC module                                  #
# Create Date:  2024-10-05                                                                  #
# Description:                                                                              #
#               Variable definitions for the VPC Terraform module                          #
#                                                                                           #
#############################################################################################

#-------------------------------------------------------------------------------------------#
# Required Variables                                                                        #
#-------------------------------------------------------------------------------------------#
variable "account" {
  description = "AWS account ID"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "common_data" {
  description = "Common data object containing shared configuration"
  type        = any
}

#-------------------------------------------------------------------------------------------#
# VPC Configuration Variables                                                               #
#-------------------------------------------------------------------------------------------#
variable "vpc_init" {
  description = "Whether to initialize VPC resources"
  type        = bool
  default     = false
}

variable "create_kms_resources" {
  description = "Whether to create KMS keys and aliases"
  type        = bool
  default     = false
}

variable "vpc_cidr_block" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
  validation {
    condition     = can(cidrhost(var.vpc_cidr_block, 0))
    error_message = "VPC CIDR block must be a valid IPv4 CIDR."
  }
}

variable "public_subnet_cidr" {
  description = "CIDR block for the public subnet"
  type        = string
  default     = "********/24"
  validation {
    condition     = can(cidrhost(var.public_subnet_cidr, 0))
    error_message = "Public subnet CIDR block must be a valid IPv4 CIDR."
  }
}

variable "availability_zone" {
  description = "Availability zone for the subnet (if empty, will use first available AZ)"
  type        = string
  default     = ""
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  type        = bool
  default     = true
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "map_public_ip_on_launch" {
  description = "Map public IP on launch for public subnet"
  type        = bool
  default     = true
}

#-------------------------------------------------------------------------------------------#
# Optional Variables                                                                        #
#-------------------------------------------------------------------------------------------#
variable "additional_tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "vpc_name_override" {
  description = "Override for VPC name (if not provided, will use environment-vpc)"
  type        = string
  default     = ""
}

variable "subnet_name_override" {
  description = "Override for subnet name (if not provided, will use environment-public-subnet)"
  type        = string
  default     = ""
}
