#!/bin/bash

# <PERSON>ript to import existing KMS keys and aliases into Terraform state
# This resolves the AlreadyExistsException errors by importing existing resources

set -e

echo "Importing existing KMS keys and aliases into Terraform state..."

# Change to the import terraform directory
cd import-terraform

# Initialize terraform if not already done
if [ ! -d ".terraform" ]; then
  echo "Initializing Terraform..."
  terraform init
fi

# Import the existing KMS keys first
echo "Importing KMS key for ddb..."
terraform import aws_kms_key.cmk[\"ddb\"] 8ccd4789-9120-463a-9f04-5c5961444177

echo "Importing KMS key for ebs..."
terraform import aws_kms_key.cmk[\"ebs\"] 4bde0c14-e9c8-48d7-ad2a-fba7b30f9211

echo "Importing KMS key for rds..."
terraform import aws_kms_key.cmk[\"rds\"] 4bf214a5-9a61-4a35-9fb3-ef08c22c29c7

echo "Importing KMS key for s3..."
terraform import aws_kms_key.cmk[\"s3\"] a1f992ea-428e-41d4-988d-edea6ece4bdf

echo "Importing KMS key for ssm..."
terraform import aws_kms_key.cmk[\"ssm\"] 87fdb370-ccf3-45c1-8daf-84105de35949

# Import the existing KMS aliases
echo "Importing alias/galaxy-ddb..."
terraform import aws_kms_alias.cmk[\"ddb\"] alias/galaxy-ddb

echo "Importing alias/galaxy-ebs..."
terraform import aws_kms_alias.cmk[\"ebs\"] alias/galaxy-ebs

echo "Importing alias/galaxy-rds..."
terraform import aws_kms_alias.cmk[\"rds\"] alias/galaxy-rds

echo "Importing alias/galaxy-s3..."
terraform import aws_kms_alias.cmk[\"s3\"] alias/galaxy-s3

echo "Importing alias/galaxy-ssm..."
terraform import aws_kms_alias.cmk[\"ssm\"] alias/galaxy-ssm

echo "All KMS keys and aliases imported successfully!"
echo "You can now run 'terraform plan' to verify the state."
