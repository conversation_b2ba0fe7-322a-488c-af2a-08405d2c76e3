{"version": 4, "terraform_version": "1.9.8", "serial": 9, "lineage": "0bf0b3b4-b769-5e46-10e9-bb2ba26c36d9", "outputs": {}, "resources": [{"mode": "data", "type": "aws_iam_policy_document", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"Enable IAM User Permissions\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"kms:*\",\n      \"Resource\": \"*\",\n      \"Principal\": {\n        \"AWS\": \"*\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"Enable IAM User Permissions\",\"Effect\":\"Allow\",\"Action\":\"kms:*\",\"Resource\":\"*\",\"Principal\":{\"AWS\":\"*\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["kms:*"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["*"], "type": "AWS"}], "resources": ["*"], "sid": "Enable IAM User Permissions"}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_iam_policy_document", "name": "s3", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "153157332", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"Enable GIT KMS Permissions\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"kms:*\",\n      \"Resource\": \"*\",\n      \"Principal\": {\n        \"AWS\": \"*\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"Enable GIT KMS Permissions\",\"Effect\":\"Allow\",\"Action\":\"kms:*\",\"Resource\":\"*\",\"Principal\":{\"AWS\":\"*\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["kms:*"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["*"], "type": "AWS"}], "resources": ["*"], "sid": "Enable GIT KMS Permissions"}], "version": "2012-10-17"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_kms_alias", "name": "cmk", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "ddb", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:alias/galaxy-ddb", "id": "alias/galaxy-ddb", "name": "alias/galaxy-ddb", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:412605689489:key/8ccd4789-9120-463a-9f04-5c5961444177", "target_key_id": "8ccd4789-9120-463a-9f04-5c5961444177"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}, {"index_key": "ebs", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:alias/galaxy-ebs", "id": "alias/galaxy-ebs", "name": "alias/galaxy-ebs", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:412605689489:key/4bde0c14-e9c8-48d7-ad2a-fba7b30f9211", "target_key_id": "4bde0c14-e9c8-48d7-ad2a-fba7b30f9211"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}, {"index_key": "rds", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:alias/galaxy-rds", "id": "alias/galaxy-rds", "name": "alias/galaxy-rds", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:412605689489:key/4bf214a5-9a61-4a35-9fb3-ef08c22c29c7", "target_key_id": "4bf214a5-9a61-4a35-9fb3-ef08c22c29c7"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}, {"index_key": "s3", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:alias/galaxy-s3", "id": "alias/galaxy-s3", "name": "alias/galaxy-s3", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:412605689489:key/a1f992ea-428e-41d4-988d-edea6ece4bdf", "target_key_id": "a1f992ea-428e-41d4-988d-edea6ece4bdf"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}]}, {"mode": "managed", "type": "aws_kms_key", "name": "cmk", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "ddb", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:key/8ccd4789-9120-463a-9f04-5c5961444177", "bypass_policy_lockout_safety_check": null, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": null, "description": "Customer Managed Key for ddb", "enable_key_rotation": true, "id": "8ccd4789-9120-463a-9f04-5c5961444177", "is_enabled": true, "key_id": "8ccd4789-9120-463a-9f04-5c5961444177", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable IAM User Permissions\"}],\"Version\":\"2012-10-17\"}", "rotation_period_in_days": 365, "tags": {}, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}, {"index_key": "ebs", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:key/4bde0c14-e9c8-48d7-ad2a-fba7b30f9211", "bypass_policy_lockout_safety_check": null, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": null, "description": "Customer Managed Key for ebs", "enable_key_rotation": true, "id": "4bde0c14-e9c8-48d7-ad2a-fba7b30f9211", "is_enabled": true, "key_id": "4bde0c14-e9c8-48d7-ad2a-fba7b30f9211", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable IAM User Permissions\"}],\"Version\":\"2012-10-17\"}", "rotation_period_in_days": 365, "tags": {}, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}, {"index_key": "rds", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:key/4bf214a5-9a61-4a35-9fb3-ef08c22c29c7", "bypass_policy_lockout_safety_check": null, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": null, "description": "Customer Managed Key for rds", "enable_key_rotation": true, "id": "4bf214a5-9a61-4a35-9fb3-ef08c22c29c7", "is_enabled": true, "key_id": "4bf214a5-9a61-4a35-9fb3-ef08c22c29c7", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable IAM User Permissions\"}],\"Version\":\"2012-10-17\"}", "rotation_period_in_days": 365, "tags": {}, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}, {"index_key": "s3", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:key/a1f992ea-428e-41d4-988d-edea6ece4bdf", "bypass_policy_lockout_safety_check": null, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": null, "description": "Customer Managed Key for s3", "enable_key_rotation": true, "id": "a1f992ea-428e-41d4-988d-edea6ece4bdf", "is_enabled": true, "key_id": "a1f992ea-428e-41d4-988d-edea6ece4bdf", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable GIT KMS Permissions\"},{\"Action\":\"s3:GotObject*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable GIT Bucket Permissions\"}],\"Version\":\"2012-10-17\"}", "rotation_period_in_days": 365, "tags": {}, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}, {"index_key": "ssm", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:412605689489:key/87fdb370-ccf3-45c1-8daf-84105de35949", "bypass_policy_lockout_safety_check": null, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": null, "description": "Customer Managed Key for ssm", "enable_key_rotation": true, "id": "87fdb370-ccf3-45c1-8daf-84105de35949", "is_enabled": true, "key_id": "87fdb370-ccf3-45c1-8daf-84105de35949", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":\"kms:*\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"*\"},\"Resource\":\"*\",\"Sid\":\"Enable IAM User Permissions\"}],\"Version\":\"2012-10-17\"}", "rotation_period_in_days": 365, "tags": {}, "tags_all": {}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}]}], "check_results": null}