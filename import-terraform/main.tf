# Minimal Terraform configuration for importing existing KMS resources
# This is used to import existing KMS keys and aliases into Terraform state

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.33"
    }
  }
}

provider "aws" {
  region              = "us-east-1"
  allowed_account_ids = [************]
  max_retries         = 50
  insecure            = false
}

# Local variables for KMS keys
locals {
  keys = {
    ddb = { policy = data.aws_iam_policy_document.default }
    ebs = { policy = data.aws_iam_policy_document.default }
    rds = { policy = data.aws_iam_policy_document.default }
    s3  = { policy = data.aws_iam_policy_document.s3 }
    ssm = { policy = data.aws_iam_policy_document.default }
  }
}

# IAM policy documents
data "aws_iam_policy_document" "default" {
  statement {
    sid       = "Enable IAM User Permissions"
    effect    = "Allow"
    actions   = ["kms:*"]
    resources = ["*"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
  }
}

data "aws_iam_policy_document" "s3" {
  statement {
    sid       = "Enable GIT KMS Permissions"
    effect    = "Allow"
    actions   = ["kms:*"]
    resources = ["*"]
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
  }
}

# KMS Keys
resource "aws_kms_key" "cmk" {
  for_each                = local.keys
  description             = format("Customer Managed Key for %s", each.key)
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  deletion_window_in_days = 7
  enable_key_rotation     = true
  is_enabled              = true
  key_usage               = "ENCRYPT_DECRYPT"
  policy                  = each.value.policy.json
}

# KMS Aliases
resource "aws_kms_alias" "cmk" {
  for_each      = local.keys
  name          = format("alias/%s-%s", "galaxy", each.key)
  target_key_id = aws_kms_key.cmk[each.key].key_id
}
